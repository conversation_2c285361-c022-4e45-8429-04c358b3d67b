<?php

namespace App\Services\WebhookService;

use App\Contracts\HttpClientInterface;
use App\Factories\Http\HttpClientFactory;
use Illuminate\Support\Facades\Log;

class ManyMessageForwarder
{
    private HttpClientInterface $httpClient;
    private string $apiKey;

    public function __construct()
    {
        $this->apiKey = config('services.manymessage.api_key', '');
        $this->httpClient = HttpClientFactory::forManyMessage();
    }

    /**
     * Check if ManyMessage forwarding is enabled and configured.
     */
    public function isEnabled(): bool
    {
        return !empty($this->apiKey) && !empty(config('services.manymessage.endpoint'));
    }

    /**
     * Check if the webhook should be forwarded to ManyMessage service.
     * Add your specific business logic here.
     */
    public function shouldForward(array $payload): bool
    {
        if (!$this->isEnabled()) {
            return false;
        }

        // Add your specific conditions here
        // For example, you might want to check:
        // - Specific webhook types
        // - Specific Instagram IDs
        // - Time-based conditions
        // - Feature flags
        
        // Example condition (replace with your actual logic):
        // return isset($payload['entry'][0]['messaging']) || isset($payload['entry'][0]['changes']);
        
        return true; // For now, forward all webhooks if service is enabled
    }

    /**
     * Forward the raw webhook payload to ManyMessage service.
     */
    public function forward(array $payload): bool
    {
        if (!$this->shouldForward($payload)) {
            return false;
        }

        try {
            $headers = [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'User-Agent' => 'DMPlus-Webhook-Forwarder/1.0',
            ];

            $body = [
                'webhook_data' => $payload,
                'timestamp' => now()->toISOString(),
                'source' => 'dmplus-webservice',
            ];

            $response = $this->httpClient->post('', $headers, $body);

            if ($response->successful()) {
                Log::info('Webhook successfully forwarded to ManyMessage', [
                    'status' => $response->status(),
                    'payload_size' => strlen(json_encode($payload)),
                ]);
                return true;
            } else {
                Log::warning('Failed to forward webhook to ManyMessage', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'payload_size' => strlen(json_encode($payload)),
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Exception while forwarding webhook to ManyMessage', [
                'error' => $e->getMessage(),
                'payload_size' => strlen(json_encode($payload)),
            ]);
            return false;
        }
    }
}
