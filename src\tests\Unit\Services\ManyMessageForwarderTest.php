<?php

namespace Tests\Unit\Services;

use App\Services\WebhookService\ManyMessageForwarder;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class ManyMessageForwarderTest extends TestCase
{
    private ManyMessageForwarder $forwarder;

    protected function setUp(): void
    {
        parent::setUp();
        $this->forwarder = new ManyMessageForwarder();
    }

    public function test_is_enabled_returns_false_when_not_configured(): void
    {
        Config::set('services.manymessage.endpoint', '');
        Config::set('services.manymessage.api_key', '');

        $this->assertFalse($this->forwarder->isEnabled());
    }

    public function test_is_enabled_returns_true_when_configured(): void
    {
        Config::set('services.manymessage.endpoint', 'https://api.manymessage.com/webhooks');
        Config::set('services.manymessage.api_key', 'test-api-key');

        $this->assertTrue($this->forwarder->isEnabled());
    }

    public function test_forward_sends_http_request_when_enabled(): void
    {
        Config::set('services.manymessage.endpoint', 'https://api.manymessage.com/webhooks');
        Config::set('services.manymessage.api_key', 'test-api-key');

        Http::fake([
            'api.manymessage.com/*' => Http::response(['status' => 'success'], 200)
        ]);

        $payload = ['test' => 'data'];
        $result = $this->forwarder->forward($payload);

        $this->assertTrue($result);

        Http::assertSent(function ($request) use ($payload) {
            return str_contains($request->url(), 'api.manymessage.com') &&
                   $request->hasHeader('Authorization') &&
                   $request->hasHeader('Content-Type', 'application/json') &&
                   isset($request->data()['webhook_data']) &&
                   $request->data()['webhook_data'] === $payload;
        });
    }
}
