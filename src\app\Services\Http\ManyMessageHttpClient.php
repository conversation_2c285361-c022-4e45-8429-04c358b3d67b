<?php

namespace App\Services\Http;

use App\Contracts\HttpClientInterface;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class ManyMessageHttpClient implements HttpClientInterface
{
    protected string $baseUrl;
    protected int $timeout;

    public function __construct(string $baseUrl, int $timeout = 30)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->timeout = $timeout;
    }

    public function get(string $path, array $headers = [], array $query = []): Response
    {
        $url = $this->baseUrl . $path;
        if (!empty($query)) {
            $url .= '?' . http_build_query($query);
        }

        return Http::withHeaders($headers)
            ->timeout($this->timeout)
            ->get($url);
    }

    public function post(string $path, array $headers, array $body = []): Response
    {
        $url = $this->baseUrl . $path;
        
        $request = Http::withHeaders($headers)->timeout($this->timeout);

        return empty($body)
            ? $request->post($url)
            : $request->post($url, $body);
    }

    public function delete(string $path, array $headers = [], array $query = []): Response
    {
        $url = $this->baseUrl . $path;
        
        return Http::withHeaders($headers)
            ->timeout($this->timeout)
            ->delete($url, $query);
    }
}
