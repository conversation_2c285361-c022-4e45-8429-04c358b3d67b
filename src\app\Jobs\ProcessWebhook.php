<?php

namespace App\Jobs;

use App\Exceptions\WebhookFormatterNotFoundException;
use App\Models\SpecialUser;
use App\Services\WebhookService\WebhookExtractor;
use App\Services\WebhookService\WebhookSender;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessWebhook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $payload;

    /**
     * Create a new job instance.
     */
    public function __construct(array $payload)
    {
        $this->payload = $payload;
    }

    /**
     * Execute the job.
     *
     * @throws ConnectionException|WebhookFormatterNotFoundException
     */
    public function handle(
        WebhookExtractor $webhookExtractor,
        WebhookSender $webhookSender
    ): void {
        /**
         * This implementation isn’t the cleanest 😅
         * We need to forward raw webhooks (before any preprocessing)
         * to the ManyMessage service so agents can review our application.
         */ 

        
        $formattedWebhook = $webhookExtractor->extract($this->payload);

        if (!$formattedWebhook) {
            return;
        }

        $instagramId = $formattedWebhook['is_admin']
            ? $formattedWebhook['sender']
            : $formattedWebhook['receiver'];

        $specialUser = SpecialUser::where('instagram_id', $instagramId)->first();

        if (!$specialUser) {
            return;
        }

        $webhookSender->sendWebhook($specialUser, $formattedWebhook);
    }
}
