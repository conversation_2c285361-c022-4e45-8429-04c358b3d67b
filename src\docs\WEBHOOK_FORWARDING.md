# Webhook Forwarding Architecture

## Overview

This document describes the clean architecture implemented for checking database records and forwarding webhooks to external services, specifically the ManyMessage service.

## Problem Statement

The original implementation had several issues:
1. Mixed concerns: database validation and external service forwarding were tightly coupled
2. Raw webhook forwarding was mentioned but not properly implemented
3. No clear separation between different forwarding logic
4. Hard to test and maintain

## Solution Architecture

### 1. Separation of Concerns

The solution separates the webhook processing into distinct steps:

1. **Raw Webhook Forwarding**: Forward unprocessed webhooks to external services
2. **Database Validation**: Check if special users exist for the webhook
3. **Processed Webhook Forwarding**: Send formatted webhooks to special user endpoints

### 2. Components

#### ManyMessageForwarder Service

**Location**: `src/app/Services/WebhookService/ManyMessageForwarder.php`

**Responsibilities**:
- Check if ManyMessage forwarding is enabled
- Determine if a webhook should be forwarded
- Forward raw webhook data to ManyMessage service
- Handle HTTP errors and logging

**Key Methods**:
- `isEnabled()`: Check if service is configured
- `shouldForward(array $payload)`: Business logic for forwarding decisions
- `forward(array $payload)`: Execute the forwarding

#### Updated ProcessWebhook Job

**Location**: `src/app/Jobs/ProcessWebhook.php`

**Changes**:
- Clean separation of forwarding and processing logic
- Dependency injection of ManyMessageForwarder
- Raw webhook forwarding happens before any processing
- Maintains existing special user processing logic

### 3. Configuration

**Location**: `src/config/services.php`

```php
'manymessage' => [
    'endpoint' => env('MANYMESSAGE_ENDPOINT'),
    'api_key' => env('MANYMESSAGE_API_KEY'),
    'timeout' => env('MANYMESSAGE_TIMEOUT', 30),
],
```

**Environment Variables**:
```env
MANYMESSAGE_ENDPOINT=https://api.manymessage.com/webhooks/receive
MANYMESSAGE_API_KEY=your_api_key_here
MANYMESSAGE_TIMEOUT=30
```

### 4. Flow Diagram

```
Webhook Received
       ↓
ProcessWebhook Job
       ↓
1. ManyMessageForwarder.forward(raw_payload)
   ├── Check if enabled
   ├── Check if should forward
   └── Send HTTP request to ManyMessage
       ↓
2. Process for Special Users
   ├── Extract and format webhook
   ├── Determine Instagram ID
   ├── Query database for SpecialUser
   └── Forward to user endpoints if found
```

## Usage Examples

### Basic Setup

1. Add environment variables to `.env`:
```env
MANYMESSAGE_ENDPOINT=https://api.manymessage.com/webhooks/receive
MANYMESSAGE_API_KEY=your_api_key_here
```

2. The service will automatically forward all webhooks when enabled.

### Custom Forwarding Logic

To customize which webhooks are forwarded, modify the `shouldForward()` method in `ManyMessageForwarder`:

```php
public function shouldForward(array $payload): bool
{
    if (!$this->isEnabled()) {
        return false;
    }

    // Example: Only forward direct messages
    if (isset($payload['entry'][0]['messaging'])) {
        return true;
    }

    // Example: Only forward specific Instagram IDs
    $instagramId = $this->extractInstagramId($payload);
    return in_array($instagramId, ['specific_id_1', 'specific_id_2']);
}
```

### Database Query Optimization

The current implementation uses a simple Eloquent query:

```php
$specialUser = SpecialUser::where('instagram_id', $instagramId)->first();
```

For better performance with large datasets, consider:

1. **Database Indexing**: Ensure `instagram_id` column is indexed
2. **Caching**: Cache frequently accessed special users
3. **Repository Pattern**: Use the existing `SpecialUserRepository` for consistency

## Testing

Run the tests to verify the implementation:

```bash
php artisan test tests/Unit/Services/ManyMessageForwarderTest.php
```

## Benefits

1. **Clean Architecture**: Clear separation of concerns
2. **Testable**: Each component can be tested independently
3. **Configurable**: Easy to enable/disable and configure
4. **Extensible**: Easy to add new forwarding services
5. **Maintainable**: Clear code structure and documentation
6. **Performance**: Raw forwarding happens before expensive processing
7. **Reliable**: Proper error handling and logging

## Future Enhancements

1. **Retry Logic**: Add exponential backoff for failed forwards
2. **Batch Processing**: Group multiple webhooks for efficiency
3. **Circuit Breaker**: Prevent cascading failures
4. **Metrics**: Add monitoring and alerting
5. **Multiple Services**: Support forwarding to multiple external services
6. **Conditional Forwarding**: More sophisticated business rules
